const { config } = require('aws-sdk');
const { NudgeRule } = require('../src/nudgeEngine');
const daysToLive = config.nudgeTTLDays || 2;
class SleepMealTimingRule extends NudgeRule {
  constructor(config = {}) {
    super("sleep_meal_gap", "Encourage early dinner before sleep.");
    this.minGapHours = config.params?.minGapHours || 4;
    this.priority = config.priority || "medium";
    this.ttl = config.ttl || 86400;
  }

  applies({ data }) {
    const lastMealTime = data.mealLogs?.date || null;
    const sleepTime = data.sleepLogs?.startTime || null;
    if (!lastMealTime || !sleepTime) return false;
    const gap = (new Date(sleepTime) - new Date(lastMealTime)) / (1000 * 60 * 60);
    return (gap > 0) && (gap < this.minGapHours);
  }

  generateNudge(data) {
    const userId = data.userId;
    const category = "steps";
    const title = "Too Close to Bedtime?";
    const message = `Try to finish your last meal at least ${this.minGapHours} hours before bedtime.`;
    return getNudgeDocument(userId, category, title, message, this.priority, this.ttl, this.ruleId);
  }
}

class LateMealNightRule extends NudgeRule {
  constructor(config = {}) {
    super("late_meal_night", "Detect late-night eating patterns.");
    this.lateHour = config.params?.lateHour || 22;
    this.daysThreshold = config.params?.daysThreshold || 2;
    this.priority = config.priority || "medium";
    this.ttl = config.ttl || 86400;
  }

  applies({ data }) {
    const recentMealLogs = data.recentMealLogs || [];
    if (!recentMealLogs || recentMealLogs.length === 0) return false;

    // Group meals by date and check for late meals
    const lateMealDays = new Set();

    recentMealLogs.forEach(meal => {
      const mealDate = new Date(meal.date);
      const mealHour = mealDate.getHours();

      // Check if meal was logged after the late hour threshold
      if (mealHour >= this.lateHour) {
        const dateKey = mealDate.toDateString();
        lateMealDays.add(dateKey);
      }
    });

    // Check if we have late meals on threshold or more days
    return lateMealDays.size >= this.daysThreshold;
  }

  generateNudge(data) {
    const userId = data.userId;
    const category = "nutrition";
    const title = "Late Night Eating Pattern";
    const message = `You've been logging late-night meals past ${this.lateHour}:00 on ${this.daysThreshold} or more days. Consider lighter meals earlier in the evening.`;
    return getNudgeDocument(userId, category, title, message, this.priority, this.ttl, this.ruleId);
  }
}

class NoRecentChatRule extends NudgeRule {
  constructor(config = {}) {
    super("no_recent_chat", "Encourage users to reconnect with wellness agent.");
    this.days = config.params?.days || 7;
    this.priority = config.priority || "low";
    this.ttl = config.ttl || 172800;
  }

  applies({ data }) {
    const lastChatInteraction = data.lastChatInteraction || null;
    if (!lastChatInteraction) {
      // If no chat interaction found, consider it as needing a nudge
      return true;
    }

    const lastInteractionDate = new Date(lastChatInteraction.timestamp);
    const currentDate = new Date();
    const daysDifference = (currentDate - lastInteractionDate) / (1000 * 60 * 60 * 24);

    return daysDifference > this.days;
  }

  generateNudge(data) {
    const userId = data.userId;
    const category = "engagement";
    const title = "Reconnect with Your Wellness Agent";
    const message = `You haven't checked in with your wellness agent in over ${this.days} days. Want to reconnect and get personalized guidance?`;
    return getNudgeDocument(userId, category, title, message, this.priority, this.ttl, this.ruleId);
  }
}

function getNudgeDocument(userId, category, title, message, priority = "medium", ttlSeconds = null, ruleId = null) {
  const currentTime = new Date();
  const ttlMilliseconds = ttlSeconds ? ttlSeconds * 1000 : daysToLive * 24 * 60 * 60 * 1000;
  const ttlDate = new Date(currentTime.getTime() + ttlMilliseconds);

  const doc = {
    userId,
    type: "nudge",
    ruleId,
    content: {
      title,
      message,
    },
    timestamp: currentTime.toISOString(),
    category,
    priority,
    ttl: ttlDate.toISOString(),
    status: "open"
  };

  return doc;
}

class BedtimeVariabilityRule extends NudgeRule {
  constructor(config = {}) {
    super("bedtime_variability", "Detect bedtime variability patterns.");
    this.shiftThresholdHrs = config.params?.shiftThresholdHrs || 2;
    this.priority = config.priority || "medium";
    this.ttl = config.ttl || 86400;
  }

  applies({ data }) {
    const recentSleepLogs = data.recentSleepLogs || [];
    if (!recentSleepLogs || recentSleepLogs.length < 3) return false;

    // Extract bedtimes from sleep logs
    const bedtimes = recentSleepLogs
      .filter(log => log.startTime)
      .map(log => {
        const bedtime = new Date(log.startTime);
        // Convert to hours (e.g., 22.5 for 10:30 PM)
        return bedtime.getHours() + bedtime.getMinutes() / 60;
      })
      .filter(time => !isNaN(time));

    if (bedtimes.length < 3) return false;

    // Calculate variability (max - min)
    const minBedtime = Math.min(...bedtimes);
    const maxBedtime = Math.max(...bedtimes);
    const variability = maxBedtime - minBedtime;

    // Handle day boundary crossing (e.g., 23:00 to 01:00)
    const adjustedVariability = variability > 12 ? 24 - variability : variability;

    return adjustedVariability > this.shiftThresholdHrs;
  }

  generateNudge(data) {
    const userId = data.userId;
    const category = "sleep";
    const title = "Inconsistent Bedtime";
    const message = `Your bedtime has varied by more than ${this.shiftThresholdHrs} hours this week. A consistent sleep schedule helps improve recovery.`;
    return getNudgeDocument(userId, category, title, message, this.priority, this.ttl, this.ruleId);
  }
}

class CgmSpikeNoMealRule extends NudgeRule {
  constructor(config = {}) {
    super("cgm_spike_no_meal", "Detect glucose spikes without meal correlation.");
    this.windowMins = config.params?.windowMins || 30;
    this.priority = config.priority || "medium";
    this.ttl = config.ttl || 86400;
  }

  applies({ data }) {
    const recentGlucoseLogs = data.recentGlucoseLogs || [];
    const recentMealLogs = data.recentMealLogs || [];

    if (!recentGlucoseLogs || recentGlucoseLogs.length < 2) return false;

    // Detect glucose spikes (significant increase from previous reading)
    const spikes = this.detectGlucoseSpikes(recentGlucoseLogs);
    if (spikes.length === 0) return false;

    // Check if any spike lacks a corresponding meal within the window
    for (const spike of spikes) {
      const spikeTime = new Date(spike.timestamp);
      const hasNearbyMeal = recentMealLogs.some(meal => {
        const mealTime = new Date(meal.date);
        const timeDiffMins = Math.abs(spikeTime - mealTime) / (1000 * 60);
        return timeDiffMins <= this.windowMins;
      });

      if (!hasNearbyMeal) {
        return true; // Found a spike without a nearby meal
      }
    }

    return false;
  }

  detectGlucoseSpikes(glucoseLogs) {
    const spikes = [];
    const sortedLogs = glucoseLogs
      .filter(log => log.value && log.timestamp)
      .sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));

    for (let i = 1; i < sortedLogs.length; i++) {
      const current = sortedLogs[i];
      const previous = sortedLogs[i - 1];

      // Define spike as increase of 30+ mg/dL or 25%+ increase
      const increase = current.value - previous.value;
      const percentIncrease = (increase / previous.value) * 100;

      if (increase >= 30 || percentIncrease >= 25) {
        spikes.push(current);
      }
    }

    return spikes;
  }

  generateNudge(data) {
    const userId = data.userId;
    const category = "nutrition";
    const title = "Glucose Spike Detected";
    const message = `Your glucose spiked and no meal was logged within ${this.windowMins} minutes of the spike. Please log your meals to help us interpret your readings.`;
    return getNudgeDocument(userId, category, title, message, this.priority, this.ttl, this.ruleId);
  }
}

class CgmSpikeThresholdRule extends NudgeRule {
  constructor(config = {}) {
    super("cgm_spike_threshold", "Detect glucose threshold crossings.");
    this.threshold = config.params?.threshold || 140;
    this.priority = config.priority || "medium";
    this.ttl = config.ttl || 86400;
  }

  applies({ data }) {
    const recentGlucoseLogs = data.recentGlucoseLogs || [];
    if (!recentGlucoseLogs || recentGlucoseLogs.length === 0) return false;

    // Check if any recent reading crossed the threshold
    return recentGlucoseLogs.some(log =>
      log.value && log.value > this.threshold
    );
  }

  generateNudge(data) {
    const userId = data.userId;
    const category = "nutrition";
    const title = "Glucose Threshold Crossed";
    const message = `Your glucose crossed ${this.threshold} mg/dL. Let's take a look at how recent meals or activity might be contributing.`;
    return getNudgeDocument(userId, category, title, message, this.priority, this.ttl, this.ruleId);
  }
}

module.exports = {
  SleepMealTimingRule,
  LateMealNightRule,
  NoRecentChatRule,
  BedtimeVariabilityRule,
  CgmSpikeNoMealRule,
  CgmSpikeThresholdRule,
};