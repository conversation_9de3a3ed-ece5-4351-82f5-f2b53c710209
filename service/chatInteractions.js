const { getOSClient } = require("../utils/connection");
const { config } = require("../environment/index");

const indexName = config.INDEX.chatInteractions;

/**
 * Get the last chat interaction for a user
 * @param {string} userId - The user ID
 * @returns {Object|null} - The last chat interaction or null if none found
 */
async function getLastChatInteraction(userId) {
  const client = getOSClient();
  
  try {
    const response = await client.search({
      index: indexName,
      body: {
        _source: { excludes: ["userId"] },
        sort: [{ "timestamp": { order: "desc" } }],
        query: {
          bool: {
            must: [{ match: { "userId.keyword": userId } }]
          }
        },
        size: 1,
      },
    });

    if (response.body?.hits?.hits?.length) {
      const hit = response.body.hits.hits[0];
      return { id: hit._id, ...hit._source };
    }
    return null;
  } catch (error) {
    // If index doesn't exist or other error, return null
    // This allows the system to work even if chat interactions aren't tracked yet
    return null;
  }
}

/**
 * Record a new chat interaction
 * @param {string} userId - The user ID
 * @param {string} interactionType - Type of interaction (e.g., 'message', 'session_start', 'session_end')
 * @param {Object} metadata - Additional metadata about the interaction
 * @returns {string|null} - The document ID or null if failed
 */
async function recordChatInteraction(userId, interactionType = 'message', metadata = {}) {
  const client = getOSClient();
  
  const document = {
    userId,
    interactionType,
    timestamp: new Date().toISOString(),
    metadata,
  };

  try {
    const response = await client.index({
      index: indexName,
      body: document,
    });
    return response.body?._id;
  } catch (error) {
    console.warn('Failed to record chat interaction:', error);
    return null;
  }
}

/**
 * Get recent chat interactions for a user within a specified number of days
 * @param {string} userId - The user ID
 * @param {number} days - Number of days to look back (default: 30)
 * @returns {Array} - Array of chat interactions
 */
async function getRecentChatInteractions(userId, days = 30) {
  const client = getOSClient();
  
  const endDate = new Date();
  const startDate = new Date();
  startDate.setDate(endDate.getDate() - days);

  try {
    const response = await client.search({
      index: indexName,
      body: {
        _source: { excludes: ["userId"] },
        sort: [{ "timestamp": { order: "desc" } }],
        query: {
          bool: {
            must: [
              { match: { "userId.keyword": userId } },
              { range: { timestamp: { gte: startDate.toISOString(), lte: endDate.toISOString() } } }
            ]
          }
        },
        size: 1000,
      },
    });

    if (response.body?.hits?.hits) {
      return response.body.hits.hits.map(hit => ({ id: hit._id, ...hit._source }));
    }
    return [];
  } catch (error) {
    // If index doesn't exist or other error, return empty array
    return [];
  }
}

module.exports = {
  getLastChatInteraction,
  recordChatInteraction,
  getRecentChatInteractions,
};
